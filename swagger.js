const swaggerAutogen = require('swagger-autogen')();

const doc = {
  info: {
    title: 'Real Estate Hub API',
    description: 'API documentation for Real Estate Hub application',
    version: '1.0.0',
  },
  host: process.env.BASE_URL || 'real-estate-hub-cmhc.onrender.com',
  schemes: ['https', 'http'],
  tags: [
    { name: 'Properties', description: 'Property management endpoints' },
    { name: 'Agents', description: 'Agent management endpoints' },
    { name: 'Clients', description: 'Client management endpoints' },
    { name: 'Users', description: 'User authentication endpoints' },
    { name: 'Authentication', description: 'Authentication endpoints' }
  ],
  definitions: {
    Property: {
      type: 'object',
      required: ['title', 'description', 'type', 'address', 'price', 'bedrooms', 'bathrooms'],
      properties: {
        title: {
          type: 'string',
          description: 'Property title',
          example: 'Beautiful 3BR House'
        },
        description: {
          type: 'string',
          description: 'Property description',
          example: 'A beautiful house with modern amenities'
        },
        type: {
          type: 'string',
          enum: ['rent', 'sale'],
          description: 'Property type',
          example: 'rent'
        },
        address: {
          type: 'string',
          description: 'Property address',
          example: '123 Main St, City, State'
        },
        price: {
          type: 'number',
          description: 'Property price',
          example: 1500
        },
        bedrooms: {
          type: 'number',
          description: 'Number of bedrooms',
          example: 3
        },
        bathrooms: {
          type: 'number',
          description: 'Number of bathrooms',
          example: 2
        },
        status: {
          type: 'string',
          enum: ['available', 'sold', 'rented'],
          description: 'Property status',
          example: 'available',
          default: 'available'
        }
      }
    },
    Agent: {
      type: 'object',
      required: ['name', 'email', 'phone', 'licenseNumber'],
      properties: {
        name: {
          type: 'string',
          description: 'Agent full name',
          example: 'John Doe'
        },
        email: {
          type: 'string',
          format: 'email',
          description: 'Agent email address',
          example: '<EMAIL>'
        },
        phone: {
          type: 'string',
          description: 'Agent phone number',
          example: '******-123-4567'
        },
        licenseNumber: {
          type: 'string',
          description: 'Agent license number',
          example: 'LIC123456'
        }
      }
    },
    Client: {
      type: 'object',
      required: ['name', 'email', 'phone'],
      properties: {
        name: {
          type: 'string',
          description: 'Client full name',
          example: 'Jane Smith'
        },
        email: {
          type: 'string',
          format: 'email',
          description: 'Client email address',
          example: '<EMAIL>'
        },
        phone: {
          type: 'string',
          description: 'Client phone number',
          example: '******-987-6543'
        }
      }
    },
    User: {
      type: 'object',
      required: ['githubId', 'username', 'email', 'profilePicture'],
      properties: {
        githubId: {
          type: 'string',
          description: 'GitHub user ID',
          example: '12345678'
        },
        username: {
          type: 'string',
          description: 'GitHub username',
          example: 'johndoe'
        },
        email: {
          type: 'string',
          format: 'email',
          description: 'User email address',
          example: '<EMAIL>'
        },
        profilePicture: {
          type: 'string',
          format: 'uri',
          description: 'User profile picture URL',
          example: 'https://avatars.githubusercontent.com/u/12345678'
        }
      }
    },
    SuccessResponse: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: true
        },
        message: {
          type: 'string',
          example: 'Operation completed successfully'
        },
        data: {
          type: 'object',
          description: 'Response data'
        }
      }
    },
    ErrorResponse: {
      type: 'object',
      properties: {
        success: {
          type: 'boolean',
          example: false
        },
        message: {
          type: 'string',
          example: 'An error occurred'
        },
        error: {
          type: 'string',
          example: 'Detailed error message'
        }
      }
    },
    ValidationError: {
      type: 'object',
      properties: {
        errors: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              msg: {
                type: 'string',
                example: 'Field is required'
              },
              param: {
                type: 'string',
                example: 'fieldName'
              },
              location: {
                type: 'string',
                example: 'body'
              }
            }
          }
        }
      }
    }
  }
};

const outputFile = './swagger-output.json';
const endpointsFiles = [
  './server.js',
  './routes/auth.js',
  './routes/properties.js',
  './routes/agents.js',
  './routes/clients.js'
];

swaggerAutogen(outputFile, endpointsFiles, doc);
