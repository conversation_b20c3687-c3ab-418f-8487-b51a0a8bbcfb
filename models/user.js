// models/user.js
// from github authentication
const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const userSchema = new Schema({
  githubId: { type: String, required: true },
  username: { type: String, required: true },
  email: { type: String, required: true },
  profilePicture: { type: String, required: true }
});

const User = mongoose.model('User', userSchema);

module.exports = User;
