// routes/auth.js
const express = require("express");
const router = express.Router();
const passport = require("passport");

// Authentication routes
router.post(
  // #swagger.tags = ['Authentication']
  // #swagger.summary = 'Login with GitHub'
  // #swagger.description = 'Initiate GitHub OAuth authentication'
  /* #swagger.responses[302] = {
    description: 'Redirect to GitHub OAuth'
  } */
  "/login", passport.authenticate("github"));

router.get(
  // #swagger.tags = ['Authentication']
  // #swagger.summary = 'GitHub OAuth callback'
  // #swagger.description = 'Handle GitHub OAuth callback'
  /* #swagger.responses[302] = {
    description: 'Redirect after authentication'
  } */
  "/github/callback",
  passport.authenticate("github", {
    failureRedirect: "/login",
    successRedirect: "/api-docs"
  }),
  (req, res) => {
    // Successful authentication, redirect home.
    res.redirect("/api-docs");
  }
);

// Logout route
router.get("/logout", async (req, res) => {
  // #swagger.tags = ['Authentication']
  // #swagger.summary = 'Logout user'
  // #swagger.description = 'Logout the current user and destroy session'
  /* #swagger.responses[200] = {
    description: 'Logout successful',
    schema: { $ref: '#/definitions/SuccessResponse' }
  } */
  /* #swagger.responses[500] = {
    description: 'Logout failed',
    schema: { $ref: '#/definitions/ErrorResponse' }
  } */
  try {
    const session = req.sessionID;

    // Destroy the session from the store
    await new Promise((resolve, reject) => {
      req.session.destroy((err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Clear cookies
  res.clearCookie("connect.sid", {
    path: "/",
    httpOnly: true,
    secure: process.env.NODE_ENV === "production"
  });

 res.status(200).json({
    success: true,
    message: "Logged out successfully"
  });

  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Logout failed"
    });
  }
});

module.exports = router;