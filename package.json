{"name": "real-estate-hub", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "lint": "eslint .", "swagger-autogen": "node swagger.js", "test": "jest"}, "repository": {"type": "git", "url": "git+https://github.com/mkatopola/real-estate-hub.git"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"connect-mongo": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "mongoose": "^8.15.1", "passport": "^0.7.0", "passport-github2": "^0.1.12", "swagger-autogen": "^2.23.7", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"eslint": "^9.28.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}