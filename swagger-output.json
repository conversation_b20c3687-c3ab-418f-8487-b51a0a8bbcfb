{"swagger": "2.0", "info": {"title": "Real Estate Hub API", "description": "API documentation for Real Estate Hub application", "version": "1.0.0"}, "host": "real-estate-hub-cmhc.onrender.com", "basePath": "/", "tags": [{"name": "Properties", "description": "Property management endpoints"}, {"name": "Agents", "description": "Agent management endpoints"}, {"name": "Clients", "description": "Client management endpoints"}, {"name": "Users", "description": "User authentication endpoints"}, {"name": "Authentication", "description": "Authentication endpoints"}], "schemes": ["https", "http"], "paths": {"/": {"get": {"tags": ["Clients"], "summary": "Get all clients", "description": "Retrieve a list of all clients", "responses": {"200": {"description": "List of clients", "schema": {"type": "object", "properties": {"type": {"type": "string", "example": "array"}, "items": {"$ref": "#/definitions/Client"}}, "xml": {"name": "main"}}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "post": {"tags": ["Clients"], "summary": "Create a new client", "description": "Create a new client with validation and duplicate check", "parameters": [{"name": "body", "in": "body", "description": "Client data", "required": true, "schema": {"$ref": "#/definitions/Client"}}], "responses": {"201": {"description": "Client created successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate client", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/auth/login": {"post": {"tags": ["Authentication"], "summary": "Login with GitHub", "description": "Initiate GitHub OAuth authentication", "responses": {"302": {"description": "Redirect to GitHub OAuth"}}}}, "/auth/github/callback": {"get": {"tags": ["Authentication"], "summary": "GitHub OAuth callback", "description": "<PERSON><PERSON> GitHub OAuth callback", "responses": {"302": {"description": "Redirect after authentication"}}}}, "/auth/logout": {"get": {"tags": ["Authentication"], "summary": "Logout user", "description": "Logout the current user and destroy session", "responses": {"200": {"description": "Logout successful", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "500": {"description": "Logout failed", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/agents/": {"post": {"tags": ["Agents"], "summary": "Create a new agent", "description": "Create a new agent with validation and duplicate check", "parameters": [{"name": "body", "in": "body", "description": "Agent data", "required": true, "schema": {"$ref": "#/definitions/Agent"}}], "responses": {"201": {"description": "Agent created successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate agent", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "get": {"tags": ["Agents"], "summary": "Get all agents", "description": "Retrieve a list of all agents", "responses": {"200": {"description": "List of agents", "schema": {"type": "object", "properties": {"type": {"type": "string", "example": "array"}, "items": {"$ref": "#/definitions/Agent"}}, "xml": {"name": "main"}}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/agents/{id}": {"get": {"tags": ["Agents"], "summary": "Get agent by ID", "description": "Retrieve a single agent by their ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Agent ID"}], "responses": {"200": {"description": "Agent found", "schema": {"$ref": "#/definitions/Agent"}}, "404": {"description": "Agent not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "put": {"tags": ["Agents"], "summary": "Update agent by ID", "description": "Update an agent by their ID with validation and duplicate check", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Agent ID"}, {"name": "body", "in": "body", "description": "Updated agent data", "required": true, "schema": {"$ref": "#/definitions/Agent"}}], "responses": {"200": {"description": "Agent updated successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate agent", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "404": {"description": "Agent not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "delete": {"tags": ["Agents"], "summary": "Delete agent by ID", "description": "Delete an agent by their ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Agent ID"}], "responses": {"200": {"description": "Agent deleted successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "404": {"description": "Agent not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/clients/": {"post": {"tags": ["Clients"], "summary": "Create a new client", "description": "Create a new client with validation and duplicate check", "parameters": [{"name": "body", "in": "body", "description": "Client data", "required": true, "schema": {"$ref": "#/definitions/Client"}}], "responses": {"201": {"description": "Client created successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate client", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "get": {"tags": ["Clients"], "summary": "Get all clients", "description": "Retrieve a list of all clients", "responses": {"200": {"description": "List of clients", "schema": {"type": "object", "properties": {"type": {"type": "string", "example": "array"}, "items": {"$ref": "#/definitions/Client"}}, "xml": {"name": "main"}}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/clients/{id}": {"get": {"tags": ["Clients"], "summary": "Get client by ID", "description": "Retrieve a single client by their ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Client ID"}], "responses": {"200": {"description": "Client found", "schema": {"$ref": "#/definitions/Client"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "put": {"tags": ["Clients"], "summary": "Update client by ID", "description": "Update a client by their ID with validation and duplicate check", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Client ID"}, {"name": "body", "in": "body", "description": "Updated client data", "required": true, "schema": {"$ref": "#/definitions/Client"}}], "responses": {"200": {"description": "Client updated successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate client", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "delete": {"tags": ["Clients"], "summary": "Delete client by ID", "description": "Delete a client by their ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Client ID"}], "responses": {"200": {"description": "Client deleted successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/properties/": {"post": {"tags": ["Properties"], "summary": "Create a new property", "description": "Create a new property with validation and duplicate check", "parameters": [{"name": "body", "in": "body", "description": "Property data", "required": true, "schema": {"$ref": "#/definitions/Property"}}], "responses": {"201": {"description": "Property created successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate property", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "get": {"tags": ["Properties"], "summary": "Get all properties", "description": "Retrieve a list of all properties", "responses": {"200": {"description": "List of properties", "schema": {"type": "object", "properties": {"type": {"type": "string", "example": "array"}, "items": {"$ref": "#/definitions/Property"}}, "xml": {"name": "main"}}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/properties/{id}": {"get": {"tags": ["Properties"], "summary": "Get property by ID", "description": "Retrieve a single property by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Property ID"}], "responses": {"200": {"description": "Property found", "schema": {"$ref": "#/definitions/Property"}}, "404": {"description": "Property not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "put": {"tags": ["Properties"], "summary": "Update property by ID", "description": "Update a property by its ID with validation and duplicate check", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Property ID"}, {"name": "body", "in": "body", "description": "Updated property data", "required": true, "schema": {"$ref": "#/definitions/Property"}}], "responses": {"200": {"description": "Property updated successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate property", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "404": {"description": "Property not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "delete": {"tags": ["Properties"], "summary": "Delete property by ID", "description": "Delete a property by its ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Property ID"}], "responses": {"200": {"description": "Property deleted successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "404": {"description": "Property not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/login": {"post": {"tags": ["Authentication"], "summary": "Login with GitHub", "description": "Initiate GitHub OAuth authentication", "responses": {"302": {"description": "Redirect to GitHub OAuth"}}}}, "/github/callback": {"get": {"tags": ["Authentication"], "summary": "GitHub OAuth callback", "description": "<PERSON><PERSON> GitHub OAuth callback", "responses": {"302": {"description": "Redirect after authentication"}}}}, "/logout": {"get": {"tags": ["Authentication"], "summary": "Logout user", "description": "Logout the current user and destroy session", "responses": {"200": {"description": "Logout successful", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "500": {"description": "Logout failed", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/{id}": {"get": {"tags": ["Clients"], "summary": "Get client by ID", "description": "Retrieve a single client by their ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Client ID"}], "responses": {"200": {"description": "Client found", "schema": {"$ref": "#/definitions/Client"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "put": {"tags": ["Clients"], "summary": "Update client by ID", "description": "Update a client by their ID with validation and duplicate check", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Client ID"}, {"name": "body", "in": "body", "description": "Updated client data", "required": true, "schema": {"$ref": "#/definitions/Client"}}], "responses": {"200": {"description": "Client updated successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "400": {"description": "Validation error or duplicate client", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}, "delete": {"tags": ["Clients"], "summary": "Delete client by ID", "description": "Delete a client by their ID", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string", "description": "Client ID"}], "responses": {"200": {"description": "Client deleted successfully", "schema": {"$ref": "#/definitions/SuccessResponse"}}, "404": {"description": "Client not found", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}}, "definitions": {"Property": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "required": {"type": "array", "example": ["title", "description", "type", "address", "price", "bedrooms", "bathrooms"], "items": {"type": "string"}}, "properties": {"type": "object", "properties": {"title": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Property title"}, "example": {"type": "string", "example": "Beautiful 3BR House"}}}, "description": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Property description"}, "example": {"type": "string", "example": "A beautiful house with modern amenities"}}}, "type": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "enum": {"type": "array", "example": ["rent", "sale"], "items": {"type": "string"}}, "description": {"type": "string", "example": "Property type"}, "example": {"type": "string", "example": "rent"}}}, "address": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Property address"}, "example": {"type": "string", "example": "123 Main St, City, State"}}}, "price": {"type": "object", "properties": {"type": {"type": "string", "example": "number"}, "description": {"type": "string", "example": "Property price"}, "example": {"type": "number", "example": 1500}}}, "bedrooms": {"type": "object", "properties": {"type": {"type": "string", "example": "number"}, "description": {"type": "string", "example": "Number of bedrooms"}, "example": {"type": "number", "example": 3}}}, "bathrooms": {"type": "object", "properties": {"type": {"type": "string", "example": "number"}, "description": {"type": "string", "example": "Number of bathrooms"}, "example": {"type": "number", "example": 2}}}, "status": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "enum": {"type": "array", "example": ["available", "sold", "rented"], "items": {"type": "string"}}, "description": {"type": "string", "example": "Property status"}, "example": {"type": "string", "example": "available"}, "default": {"type": "string", "example": "available"}}}}}}}, "Agent": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "required": {"type": "array", "example": ["name", "email", "phone", "licenseNumber"], "items": {"type": "string"}}, "properties": {"type": "object", "properties": {"name": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Agent full name"}, "example": {"type": "string", "example": "<PERSON>"}}}, "email": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "format": {"type": "string", "example": "email"}, "description": {"type": "string", "example": "Agent email address"}, "example": {"type": "string", "example": "<EMAIL>"}}}, "phone": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Agent phone number"}, "example": {"type": "string", "example": "******-123-4567"}}}, "licenseNumber": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Agent license number"}, "example": {"type": "string", "example": "LIC123456"}}}}}}}, "Client": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "required": {"type": "array", "example": ["name", "email", "phone"], "items": {"type": "string"}}, "properties": {"type": "object", "properties": {"name": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Client full name"}, "example": {"type": "string", "example": "<PERSON>"}}}, "email": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "format": {"type": "string", "example": "email"}, "description": {"type": "string", "example": "Client email address"}, "example": {"type": "string", "example": "<EMAIL>"}}}, "phone": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "Client phone number"}, "example": {"type": "string", "example": "******-987-6543"}}}}}}}, "User": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "required": {"type": "array", "example": ["githubId", "username", "email", "profilePicture"], "items": {"type": "string"}}, "properties": {"type": "object", "properties": {"githubId": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "GitHub user ID"}, "example": {"type": "string", "example": "12345678"}}}, "username": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "description": {"type": "string", "example": "GitHub username"}, "example": {"type": "string", "example": "johndoe"}}}, "email": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "format": {"type": "string", "example": "email"}, "description": {"type": "string", "example": "User email address"}, "example": {"type": "string", "example": "<EMAIL>"}}}, "profilePicture": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "format": {"type": "string", "example": "uri"}, "description": {"type": "string", "example": "User profile picture URL"}, "example": {"type": "string", "example": "https://avatars.githubusercontent.com/u/12345678"}}}}}}}, "SuccessResponse": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"success": {"type": "object", "properties": {"type": {"type": "string", "example": "boolean"}, "example": {"type": "boolean", "example": true}}}, "message": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "Operation completed successfully"}}}, "data": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "description": {"type": "string", "example": "Response data"}}}}}}}, "ErrorResponse": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"success": {"type": "object", "properties": {"type": {"type": "string", "example": "boolean"}, "example": {"type": "boolean", "example": false}}}, "message": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "An error occurred"}}}, "error": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "Detailed error message"}}}}}}}, "ValidationError": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"errors": {"type": "object", "properties": {"type": {"type": "string", "example": "array"}, "items": {"type": "object", "properties": {"type": {"type": "string", "example": "object"}, "properties": {"type": "object", "properties": {"msg": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "Field is required"}}}, "param": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "fieldName"}}}, "location": {"type": "object", "properties": {"type": {"type": "string", "example": "string"}, "example": {"type": "string", "example": "body"}}}}}}}}}}}}}}}