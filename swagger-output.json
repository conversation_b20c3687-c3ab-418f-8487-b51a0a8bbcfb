{"swagger": "2.0", "info": {"title": "Real Estate Hub API", "description": "API documentation for Real Estate Hub application", "version": "1.0.0"}, "host": "real-estate-hub-cmhc.onrender.com", "basePath": "/", "tags": [{"name": "Properties", "description": "Property management endpoints"}, {"name": "Agents", "description": "Agent management endpoints"}, {"name": "Clients", "description": "Client management endpoints"}, {"name": "Users", "description": "User authentication endpoints"}, {"name": "Authentication", "description": "Authentication endpoints"}], "schemes": ["https", "http"], "paths": {"/": {"get": {"description": "", "responses": {"200": {"description": "OK"}}}}, "/agents/": {"post": {"tags": ["Agents"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"email": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}}}, "get": {"tags": ["Agents"], "description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/agents/{id}": {"get": {"tags": ["Agents"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["Agents"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"email": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/clients/": {"post": {"tags": ["Clients"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"email": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}}}, "get": {"tags": ["Clients"], "description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/clients/{id}": {"get": {"tags": ["Clients"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["Clients"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"email": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Clients"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/properties/": {"post": {"tags": ["Properties"], "description": "", "parameters": [{"name": "body", "in": "body", "schema": {"type": "object", "properties": {"title": {"example": "any"}}}}], "responses": {"201": {"description": "Created"}, "400": {"description": "Bad Request"}}}, "get": {"tags": ["Properties"], "description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}, "/properties/{id}": {"get": {"tags": ["Properties"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "put": {"tags": ["Properties"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}, {"name": "body", "in": "body", "schema": {"type": "object", "properties": {"title": {"example": "any"}}}}], "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}, "delete": {"tags": ["Properties"], "description": "", "parameters": [{"name": "id", "in": "path", "required": true, "type": "string"}], "responses": {"200": {"description": "OK"}, "404": {"description": "Not Found"}, "500": {"description": "Internal Server Error"}}}}, "/login": {"post": {"tags": ["Authentication"], "description": "", "responses": {"default": {"description": ""}}}}, "/github/callback": {"get": {"tags": ["Authentication"], "description": "", "responses": {"default": {"description": ""}}}}, "/logout": {"get": {"tags": ["Authentication"], "description": "", "responses": {"200": {"description": "OK"}, "500": {"description": "Internal Server Error"}}}}}, "definitions": {"Property": {"type": "object", "properties": {"title": {"type": "string", "example": "Sample Property"}, "description": {"type": "string", "example": "Property description"}, "type": {"type": "string", "example": "rent"}, "address": {"type": "string", "example": "123 Main St"}, "price": {"type": "number", "example": 1500}, "bedrooms": {"type": "number", "example": 3}, "bathrooms": {"type": "number", "example": 2}, "status": {"type": "string", "example": "available"}}}, "Agent": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "************"}, "licenseNumber": {"type": "string", "example": "ABC123"}}}, "Client": {"type": "object", "properties": {"name": {"type": "string", "example": "<PERSON>"}, "email": {"type": "string", "example": "<EMAIL>"}, "phone": {"type": "string", "example": "************"}}}, "User": {"type": "object", "properties": {"githubId": {"type": "string", "example": "12345"}, "username": {"type": "string", "example": "johndoe"}, "email": {"type": "string", "example": "<EMAIL>"}, "profilePicture": {"type": "string", "example": "https://example.com/pic.jpg"}}}}}